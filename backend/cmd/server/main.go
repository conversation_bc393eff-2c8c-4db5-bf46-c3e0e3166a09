package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"

	"github.com/denilany/mesaka/internal/auth"
	"github.com/denilany/mesaka/internal/common"
	"github.com/denilany/mesaka/internal/routes"
	"github.com/denilany/mesaka/internal/users"
	"github.com/denilany/mesaka/pkg/db"
)

func main() {
	// Load configuration
	config, err := common.LoadConfig()
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	// Set Gin mode
	gin.SetMode(config.Server.Mode)

	// Initialize database connection
	dbConfig := db.Config{
		Host:     config.Database.Host,
		Port:     config.Database.Port,
		User:     config.Database.User,
		Password: config.Database.Password,
		DBName:   config.Database.DBName,
		SSLMode:  config.Database.SSLMode,
	}

	database, err := db.NewConnection(dbConfig)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}
	defer database.Close()

	// Initialize Redis client
	redisClient := redis.NewClient(&redis.Options{
		Addr:     fmt.Sprintf("%s:%s", config.Redis.Host, config.Redis.Port),
		Password: config.Redis.Password,
		DB:       config.Redis.DB,
	})

	// Test Redis connection
	ctx := context.Background()
	if err := redisClient.Ping(ctx).Err(); err != nil {
		log.Fatalf("Failed to connect to Redis: %v", err)
	}
	log.Println("Redis connection established successfully")

	// Initialize services
	userRepo := users.NewRepository(database)
	jwtService := auth.NewJWTService(
		config.JWT.SecretKey,
		config.JWT.AccessTokenDuration,
		config.JWT.RefreshTokenDuration,
	)
	redisService := auth.NewRedisService(redisClient, config.JWT.RefreshTokenDuration)
	authService := auth.NewService(userRepo, jwtService, redisService)

	// Initialize handlers
	authHandler := auth.NewHandler(authService)

	// Setup router
	routeDeps := routes.Dependencies{
		AuthHandler: authHandler,
		JWTService:  jwtService,
	}
	router := routes.SetupRoutes(routeDeps)

	// Setup server
	server := &http.Server{
		Addr:    fmt.Sprintf("%s:%s", config.Server.Host, config.Server.Port),
		Handler: router,
	}

	// Start server in a goroutine
	go func() {
		log.Printf("Server starting on %s:%s", config.Server.Host, config.Server.Port)
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("Failed to start server: %v", err)
		}
	}()

	// Wait for interrupt signal to gracefully shutdown the server
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	log.Println("Shutting down server...")

	// Graceful shutdown with timeout
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := server.Shutdown(ctx); err != nil {
		log.Fatalf("Server forced to shutdown: %v", err)
	}

	log.Println("Server exited")
}
