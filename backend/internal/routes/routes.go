package routes

import (
	"net/http"
	"time"

	"github.com/gin-gonic/gin"

	"github.com/denilany/mesaka/internal/auth"
)

// Dependencies holds all the dependencies needed for route setup
type Dependencies struct {
	AuthHandler *auth.Handler
	JWTService  *auth.JWTService
}

// SetupRoutes configures the Gin router with all routes and middleware
func SetupRoutes(deps Dependencies) *gin.Engine {
	router := gin.New()

	// Add global middleware
	router.Use(gin.Logger())
	router.Use(gin.Recovery())
	router.Use(auth.CORSMiddleware())

	// Health check endpoint
	router.GET("/health", healthCheckHandler)

	// API v1 routes
	v1 := router.Group("/api/v1")
	{
		// Authentication routes
		authGroup := v1.Group("/auth")
		auth.SetupRoutes(authGroup, deps.AuthHandler)
	}

	return router
}

// healthCheckHandler handles the health check endpoint
func healthCheckHandler(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"status":    "ok",
		"timestamp": time.Now().Unix(),
	})
}
